import {
  Box,
  Button,
  Checkbox,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  IconButton,
  Radio,
  RadioGroup,
  Stack,
  Tooltip,
  Typography,
  TextField,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import useLang from "@hooks/useLang";
import { useDispatch, useSelector } from "react-redux";
import {
  BUTTON_NAME,
  DECISION_TABLE_NAME,
  ENABLE_STATUSES,
  ERROR_MESSAGES,
  LOCAL_STORAGE_KEYS,
  MODULE,
  MODULE_MAP,
  REGION_CODE,
  REQUEST_TYPE,
  SUCCESS_MESSAGES,
} from "@constant/enum";
import CloseFullscreenIcon from "@mui/icons-material/CloseFullscreen";
import CropFreeIcon from "@mui/icons-material/CropFree";
import { DataGrid } from "@mui/x-data-grid";
import useDynamicWorkflowDT from "@hooks/useDynamicWorkflowDT";
import { useLocation } from "react-router-dom";
import { v4 as uuidv4 } from "uuid";
import {
  CancelOutlined as CancelOutlinedIcon,
  DeleteOutlineOutlined as DeleteOutlineOutlinedIcon,
  TaskAlt as TaskAltIcon,
} from "@mui/icons-material";
import {
  setBomRows,
  setSelectedRowID,
  clearTabData,
  setButtonDTData,
  setRowValidationStatus,
} from "@BillOfMaterial/bomSlice";
import { updatePage } from "@app/paginationSlice";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import useGenericDtCall from "@hooks/useGenericDtCall";
import BOMViews from "./components/BOMViews";
import { useSnackbar } from "@hooks/useSnackbar";
import BottomNavGlobal from "@components/RequestBench/RequestPages/BottomNavGlobal";
import AddBOMDialog from "./components/AddBOMDialog";
import dayjs from "dayjs";
import { filterButtonsBasedOnTab } from "@helper/helper";
import { BUTTONS_ACTION_TYPE } from "@constant/buttonPriority";
import { CreatebuttonPriority } from "@constant/buttonPriority";
import { getLocalStorage } from "@helper/helper";
import useBomBottomButtons from "./hooks/useBomBottomButtons";
import { doAjax } from "@components/Common/fetchService";
import { destination_BOM } from "../../destinationVariables";
import useBomDuplicateCheck from "./hooks/useBomDuplicateCheck";

const BOMListDetails = (props) => {
  const { t } = useLang();
  const dispatch = useDispatch();
  const { getDtCall, dtData } = useGenericDtCall();
  const { getDtCall: getButtonDT, dtData: dtButtonData } = useGenericDtCall();
  const { showSnackbar } = useSnackbar();
  const { handleBottomButton } = useBomBottomButtons();
  const { checkBomDuplicates, result: bomDupResult, error: bomDupError, loading: bomDupLoading } = useBomDuplicateCheck();

  const payloadFields = useSelector((state) => state.bom.BOMpayloadData);
  const storedRows = useSelector((state) => state.bom.bomRows);
  const selectedRowID = useSelector((state) => state.bom.selectedRowID);
  const paginationData = useSelector((state) => state.paginationData);
  const allDropDownData = useSelector((state) => state.bom.dropDownData || {});
  const taskData = useSelector((state) => state.userManagement.taskData);
  const buttonDTData = useSelector((state) => state.bom.buttonDTData);
  const validatedRows = useSelector((state) => state.bom.validatedRows || {});
  const tabFieldValues = useSelector((state) => state.bom.tabFieldValues);
  const [rows, setRows] = useState(storedRows || []);
  const [openAddMatPopup, setOpenAddMatPopup] = useState(false);
  const [materialOptions, setMaterialOptions] = useState([]);
  const [timerId, setTimerId] = useState(null);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [viewNames, setViewNames] = useState([]);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const [withReference, setWithReference] = useState("yes");
  const [page, setPage] = useState(0);
  const [dupDialogOpen, setDupDialogOpen] = useState(false);
  const [dupDialogMsg, setDupDialogMsg] = useState("");
  const [plantOptions, setPlantOptions] = useState({});
  const [isAddRowEnabled, setIsAddRowEnabled] = useState(true);
  const [wfLevels,setWfLevels] = useState([]);
  const [showWfLevels, setShowWfLevels] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState('');
  const { getDynamicWorkflowDT } = useDynamicWorkflowDT();
  useEffect(() => {
      const fetchWorkflowLevels = async () => {
        try {
          const workflowLevelsDtData = await getDynamicWorkflowDT(
            payloadFields?.RequestType,
            payloadFields?.Region,
            '',
            payloadFields?.Tochildrequestheaderdata?.BOMGroupType,
            taskData?.ATTRIBUTE_3,
            "v1",
            "MDG_BOM_DYNAMIC_WORKFLOW_DT",
            MODULE_MAP?.BOM
          );
          setWfLevels(workflowLevelsDtData);
        } catch (err) {
          showSnackbar(err?.message, "error");
        }
      };
      if (payloadFields?.RequestType && payloadFields?.Region  && taskData?.ATTRIBUTE_3) {
        fetchWorkflowLevels();
      }
    }, [payloadFields?.RequestType, payloadFields?.Region,taskData?.ATTRIBUTE_3]);

  useEffect(() => {
    setRows(storedRows || []);
  }, [storedRows]);

  useEffect(() => {
    if (rows.length > 0 && !selectedRowID) {
      dispatch(setSelectedRowID(rows[0].id));
    }
  }, [rows, selectedRowID, dispatch]);

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isrequestId = queryParams.get("RequestId");
  const disableCheck =
    !ENABLE_STATUSES.includes(props?.requestStatus) ||
    (isrequestId && !isreqBench);

  useEffect(() => {
    if (
      storedRows?.length === 0 &&
      (payloadFields?.RequestType === REQUEST_TYPE.CREATE ||
        payloadFields?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD)
    ) {
      setOpenAddMatPopup(true);
    }
    const payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_BOM_CONFIG,
      version: "v3",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_REGION": REGION_CODE.US,
          "MDG_CONDITIONS.MDG_GROUP_ROLE": taskData?.ATTRIBUTE_5 || "Z_MAT_REQ_INITIATE",
          "MDG_CONDITIONS.MDG_SCENARIO": REQUEST_TYPE.CREATE,
        },
      ],
    };
    getDtCall(payload);
    fetchButtonsFromDt();
  }, []);

  useEffect(() => {
    if (dtData?.result?.length) {
      const viewNameSequenceMap = new Map();
      dtData.result.forEach((item) => {
        item.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE?.forEach((field) => {
          const viewName = field.MDG_VIEW_NAME;
          const sequence = field.MDG_VIEW_SEQUENCE;

          if (viewName && viewName !== "Header" && sequence !== undefined) {
            if (
              !viewNameSequenceMap.has(viewName) ||
              viewNameSequenceMap.get(viewName) > sequence
            ) {
              viewNameSequenceMap.set(viewName, sequence);
            }
          }
        });
      });

      const sortedViewNames = Array.from(viewNameSequenceMap.entries())
        .sort(([, sequenceA], [, sequenceB]) => sequenceA - sequenceB)
        .map(([viewName]) => viewName);

      setViewNames(sortedViewNames);
    }
  }, [dtData]);

  useEffect(() => {
    if (dtButtonData) {
      dispatch(setButtonDTData(dtButtonData));
    }
  }, [dtButtonData, dispatch]);

  const fetchButtonsFromDt = () => {
    const payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_BOM_BUTTONS,
      version: "v3",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME": MODULE.BOM,
          "MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":
            payloadFields?.RequestType,
        },
      ],
    };
    getButtonDT(payload);
  };

  useEffect(() => {
    if (rows.length > 0) {
      const initialValidationState = {};
      rows.forEach((row) => {
        if (!(row.id in validatedRows)) {
          initialValidationState[row.id] = undefined;
        }
      });
      dispatch(setRowValidationStatus(initialValidationState));
    }
  }, [rows, validatedRows, dispatch]);

  const handleCellEdit = (params) => {
    const { id, field, value } = params;
    const dropdownFields = ["material", "plant", "bomUsage"];
    let normalizedValue = value;
    if (dropdownFields.includes(field)) {
      if (typeof value === "object" && value !== null) {
        normalizedValue = value.code || "";
      }
    }
    let updatedRows = rows.map((row) =>
      row.id === id
        ? {
            ...row,
            [field]:
              (field === "validFrom" || field === "validTo") && normalizedValue
                ? dayjs(normalizedValue)
                : normalizedValue,
            ...(field === "material" ? { plant: "" } : {}),
          }
        : row
    );
    setRows(updatedRows);
    dispatch(setBomRows(updatedRows));

    if (field === "material") {
      getPlantOptions(value, id);
    }

    const mandatoryFields = ["material", "plant", "bomUsage", "altBom", "bomDescription"];
    if (mandatoryFields.includes(field)) {
      dispatch(setRowValidationStatus({ rowId: id, status: undefined }));
    }
  };

  const  handleMatInputChange = (e) => {
    const inputValue = e.target.value;
    if (timerId) {
      clearTimeout(timerId);
    }

    const newTimerId = setTimeout(() => {
      getMaterialSearch(inputValue);
    }, 500);
    setTimerId(newTimerId);
  };

  const getMaterialSearch = (inputValue = "") => {
    setIsDropDownLoading(true);
    let payload = {
      materialNo: inputValue,
      top: 200,
      skip: 0
    };
    const hSuccess = (data) => {
      setIsDropDownLoading(false);
      
      const selectedMaterials = rows
        .map(row => row.material)
        .filter(mat => mat && typeof mat === 'object' && mat.code);
     
      const newOptions = data.body || [];
      setMaterialOptions(prevOptions => {
       
        const allOptions = [
          ...prevOptions,
          ...selectedMaterials.filter(
            sel => !prevOptions.some(opt => opt.code === sel.code)
          ),
          ...newOptions.filter(
            opt => !prevOptions.some(prev => prev.code === opt.code)
          )
        ];
        
        const uniqueOptions = [];
        const seenCodes = new Set();
        for (const opt of allOptions) {
          if (!seenCodes.has(opt.code)) {
            uniqueOptions.push(opt);
            seenCodes.add(opt.code);
          }
        }
        return uniqueOptions;
      });
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_BOM}/data/getMaterialBasedOnMaterialNo`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getPlantOptions = (material, rowId) => {
    if (!material) {
      setPlantOptions(prev => ({ ...prev, [rowId]: [] }));
      return;
    }
    doAjax(
      `/${destination_BOM}/data/getPlantBasedOnMaterial?material=${encodeURIComponent(typeof material === 'object' ? material.code : material)}`,
      "get",
      (data) => {
        setPlantOptions(prev => ({ ...prev, [rowId]: data.body || [] }));
      },
      (error) => {
        setPlantOptions(prev => ({ ...prev, [rowId]: [] }));
        console.error(error);
      }
    );
  };

  const handlePageChange = (newPage) => {
    dispatch(updatePage(newPage));
    setPage(newPage);
  };
  const handleRowSelection = (params) => {
    dispatch(setSelectedRowID(params.row.id));
  };

  const AddBOM = () => {
    setOpenAddMatPopup(false);
    if (withReference === "yes") {
      return;
    } else {
      handleAddRow();
    }
  };

  const handleAddRow = () => {
    const id = uuidv4();
    const newRow = {
      id,
      included: true,
      material: "",
      plant: "",
      bomUsage: "",
      altBom: "",
      bomDescription: "",
      validFrom: dayjs(),
      validTo: dayjs(),
    };
    dispatch(setBomRows([...rows, newRow]));
    setRows([...rows, newRow]);
    dispatch(setRowValidationStatus({ rowId: id, status: undefined }));
  };

  const checkDuplicateCombination = (currentRow) => {
    const { material, plant, bomUsage, altBom, bomDescription } = currentRow;
    const getDisplayValue = (val) => {
      if (!val) return '';
      if (typeof val === 'object') {
        return val.code || val.desc || '';
      }
      return val;
    };
    const missingFields = [];
    if (!material) missingFields.push(t("Material"));
    if (!plant) missingFields.push(t("Plant"));
    if (!bomUsage) missingFields.push(t("BOM Usage"));
    if (!altBom) missingFields.push(t("Alternative BOM"));
    if (!bomDescription) missingFields.push(t("BOM Description"));

    if (missingFields.length > 0) {
      return {
        isValid: false,
        message: t("Please fill the following mandatory field(s): ") + missingFields.join(", "),
      };
    }

    const currentTabData = tabFieldValues[selectedRowID] || {};
    const viewsDt = dtData?.result;
    const missingTabFields = [];
    if (viewsDt) {
      viewsDt.forEach((viewConfig) => {
        const viewName = viewConfig.MDG_VIEW_NAME;
        if (viewName === "Material" || viewName === "Document") {
          const fields = viewConfig.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE || [];
          const missingInTab = [];
          fields.forEach((field) => {
            if (field.MDG_VISIBILITY === "Mandatory") {
              const fieldName = field.MDG_JSON_FIELD_NAME || field.MDG_UI_FIELD_NAME;
              const value = currentTabData[viewName]?.[fieldName];
              if (value === undefined || value === null || (typeof value === "string" && value.trim() === "")) {
                missingInTab.push(t(field.MDG_UI_FIELD_NAME));
              }
            }
          });
          if (missingInTab.length > 0) {
            missingTabFields.push(`${t(viewName)}: ${missingInTab.join(", ")}`);
          }
        }
      });
    }

    if (missingTabFields.length > 0) {
      return {
        isValid: false,
        message: t("Please fill the following mandatory field(s) in tab(s): ") + missingTabFields.join("; "),
      };
    }

    const duplicateRows = rows
      .map((row, idx) => ({ row, idx }))
      .filter(({ row }) =>
        row.id !== currentRow.id &&
        getDisplayValue(row.material) === getDisplayValue(material) &&
        getDisplayValue(row.plant) === getDisplayValue(plant) &&
        getDisplayValue(row.bomUsage) === getDisplayValue(bomUsage) &&
        getDisplayValue(row.altBom) === getDisplayValue(altBom)
      );

    if (duplicateRows.length > 0) {
      const currentIndex = rows.findIndex(row => row.id === currentRow.id);
      const duplicateIndex = duplicateRows[0].idx;
      return {
        isValid: false,
        message: `${t(ERROR_MESSAGES.DUPLICATE_COMBINATION)}: Row ${currentIndex + 1} and Row ${duplicateIndex + 1} have the same combination (Material, Plant, BOM Usage, Alternative BOM)`,
      };
    }

    return {
      isValid: true,
      message: t(SUCCESS_MESSAGES.DUPLICATE_COMBINATION),
    };
  };

  const handleValidateRow = async (row) => {
    const validation = checkDuplicateCombination(row);

    if (!validation.isValid) {
      showSnackbar(validation.message, "error");
      dispatch(setRowValidationStatus({ rowId: row.id, status: false }));
      return;
    }

    try {
      const apiResult = await checkBomDuplicates(row);
      if (apiResult && apiResult.statusCode === 200) {
        showSnackbar(apiResult.message, "success");
        dispatch(setRowValidationStatus({ rowId: row.id, status: true }));
      } else if (apiResult && apiResult.statusCode === 500) {
        setDupDialogMsg(apiResult.message);
        setDupDialogOpen(true);
        dispatch(setRowValidationStatus({ rowId: row.id, status: false }));
      } else {
        showSnackbar("Unexpected response from duplicate check.", "error");
        dispatch(setRowValidationStatus({ rowId: row.id, status: false }));
      }
    } catch (err) {
      showSnackbar("Error checking duplicates: " + (err.message || err), "error");
      dispatch(setRowValidationStatus({ rowId: row.id, status: false }));
    }
  };

  useEffect(() => {
    const allRowsValidated = rows.length === 0 || 
      (rows.length > 0 && rows.every(row => validatedRows[row.id] === true));
    setIsAddRowEnabled(allRowsValidated);
  }, [rows, validatedRows]);

  const columns = [
    {
      field: "included",
      headerName: t("Included"),
      flex: 0.4,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          disabled={disableCheck}
          onChange={(e) =>
            handleCellEdit({
              id: params.row.id,
              field: "included",
              value: e.target.checked,
            })
          }
        />
      ),
    },
    {
      field: "sno",
      headerName: "S.No",
      flex: 0.2,
      align: "center",
      headerAlign: "center",
      sortable: false,
      filterable: false,
      renderCell: (params) => {
        const index = rows.findIndex(row => row.id === params.row.id);
        return index !== -1 ? index + 1 : '';
      },
    },
    {
      field: "material",
      headerName: (
        <span>
          {t("Material")}<span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.8,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {

        const materialValue =
          materialOptions.find(opt => opt.code === params.row.material) ||
          (params.row.material
            ? { code: params.row.material, desc: "" }
            : "");

        const optionsWithFallback =
          materialValue && materialValue.code && !materialOptions.some(opt => opt.code === materialValue.code)
            ? [...materialOptions, materialValue]
            : materialOptions;

        return (
          <div style={{ width: "100%" }}>
            <SingleSelectDropdown
              options={optionsWithFallback || []}
              value={materialValue}
              onChange={(newValue) =>
                handleCellEdit({
                  id: params.row.id,
                  field: "material",
                  value: newValue || "",
                })
              }
              handleInputChange={handleMatInputChange}
              isLoading={isDropDownLoading}
              placeholder={t("Select Material")}
              disabled={disableCheck}
              minWidth="90%"
              listWidth={235}
            />
          </div>
        );
      },
    },
    {
      field: "plant",
      headerName: (
        <span>
          {t("Plant")}<span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.8,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const plantValue =
          (plantOptions[params.row.id] || []).find(opt => opt.code === params.row.plant) ||
          (params.row.plant
            ? { code: params.row.plant, desc: "" }
            : "");

        const optionsWithFallback =
          plantValue && plantValue.code && !(plantOptions[params.row.id] || []).some(opt => opt.code === plantValue.code)
            ? [...(plantOptions[params.row.id] || []), plantValue]
            : (plantOptions[params.row.id] || []);

        return (
          <SingleSelectDropdown
            options={optionsWithFallback}
            value={plantValue}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "plant",
                value: newValue || "",
              })
            }
            placeholder={t("Select Plant")}
            disabled={disableCheck}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "bomUsage",
      headerName: (
        <span>
          {t("BOM Usage")}<span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.8,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["BOMUsage"] || []}
            value={allDropDownData?.["BOMUsage"]?.find(opt => opt.code === params.row.bomUsage) || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "bomUsage",
                value: newValue || "",
              })
            }
            placeholder={t("Select BOM Usage")}
            disabled={disableCheck}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "altBom",
      headerName: (
        <span>
          {t("Alternative BOM")}<span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.8,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <TextField
          value={params.row.altBom || ""}
          onChange={(e) => {
            const inputValue = e.target.value.toUpperCase();
            const cleanedValue = inputValue.replace(/[^A-Z0-9-]/g, "").slice(0, 40);
            handleCellEdit({
              id: params.row.id,
              field: "altBom",
              value: cleanedValue,
            });
          }}
          placeholder={t("ENTER ALTERNATIVE BOM")}
          disabled={disableCheck}
          inputProps={{ maxLength: 40 }}
          size="small"
          variant="outlined"
          sx={{ width: "90%" }}
        />
        );
      },
    },
    {
      field: "bomDescription",
      headerName: (
        <span>
          {t("BOM Description")}<span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <TextField
          value={params.row.bomDescription || ""}
          onChange={(e) => {
            const inputValue = e.target.value.toUpperCase();
            const cleanedValue = inputValue.replace(/[^A-Z0-9-]/g, "").slice(0, 40);
            handleCellEdit({
              id: params.row.id,
              field: "bomDescription",
              value: cleanedValue,
            });
          }}
          placeholder={t("ENTER BOM DESCRIPTION")}
          disabled={disableCheck}
          inputProps={{ maxLength: 40 }}
          size="small"
          variant="outlined"
          sx={{ width: "90%" }}
        />
      ),
    },
    {
      field: "validFrom",
      headerName: t("Valid From"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              value={params.row.validFrom}
              onChange={(newValue) =>
                handleCellEdit({
                  id: params.row.id,
                  field: "validFrom",
                  value: newValue,
                })
              }
              disabled={disableCheck}
              format="DD/MM/YYYY"
              slotProps={{
                textField: {
                  sx: {
                    width: "90%",
                    "& .MuiInputBase-root": {
                      height: 36,
                    },
                    "& .MuiInputBase-input": {
                      padding: "8.5px 14px",
                    },
                  },
                  size: "small",
                },
              }}
            />
          </LocalizationProvider>
        );
      },
    },
    {
      field: "validTo",
      headerName: t("Valid To"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              value={params.row.validTo}
              onChange={(newValue) =>
                handleCellEdit({
                  id: params.row.id,
                  field: "validTo",
                  value: newValue,
                })
              }
              disabled={disableCheck}
              format="DD/MM/YYYY"
              slotProps={{
                textField: {
                  sx: {
                    width: "90%",
                    "& .MuiInputBase-root": {
                      height: 36,
                    },
                    "& .MuiInputBase-input": {
                      padding: "8.5px 14px",
                    },
                  },
                  size: "small",
                },
              }}
            />
          </LocalizationProvider>
        );
      },
    },
    {
      field: "Actions",
      headerName: t("Actions"),
      flex: 0.6,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const validateStatus = validatedRows[params.row.id] || undefined;
        return (
          <Stack
            direction="row"
            alignItems="center"
            sx={{ marginLeft: "0.5rem", magrinRight: "0.5rem" }}
            spacing={0.5}
          >
            
            <Tooltip
              title={
                validateStatus === true
                  ? "Validated Successfully"
                  : validateStatus === false
                  ? t("Validation Failed")
                  : t("Click to Validate")
              }
            >
              <IconButton
                onClick={() => handleValidateRow(params.row)}
                disabled={disableCheck}
                color={
                  validateStatus === true
                    ? "success"
                    : validateStatus === false
                    ? "error"
                    : "default"
                }
              >
                {validateStatus === false ? (
                  <CancelOutlinedIcon />
                ) : (
                  <TaskAltIcon />
                )}
              </IconButton>
            </Tooltip>
            {!disableCheck && (
              <Tooltip title="Delete Row">
                <IconButton
                  onClick={() => {
                    const updatedRows = rows.filter(
                      (row) => row.id !== params.row.id
                    );
                    setRows(updatedRows);
                    dispatch(setBomRows(updatedRows));
                    dispatch(clearTabData(params.row.id));
                    dispatch(setRowValidationStatus({ rowId: params.row.id, status: undefined }));
                  }}
                  color="error"
                >
                  <DeleteOutlineOutlinedIcon />
                </IconButton>
              </Tooltip>
            )}
          </Stack>
        );
      },
    },
  ];

  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
    if (isTabsZoomed) setIsTabsZoomed(false);
  };

  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed) setIsGridZoomed(false);
  };

  const savedTask = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, true, {});
  const effectiveTaskDesc = (savedTask && savedTask.taskDesc) || (taskData && taskData.taskDesc);

  const rawButtons = buttonDTData?.result?.[0]?.MDG_MAT_DYN_BUTTON_CONFIG || [];
  const taskFilteredButtons = rawButtons.filter(
    btn => btn.MDG_MAT_DYN_BTN_TASK_NAME === effectiveTaskDesc
  );
  const filteredButtons = filterButtonsBasedOnTab(
    taskFilteredButtons,
    [
      BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_APPROVAL,
      BUTTONS_ACTION_TYPE.HANDLE_SAP_SYNDICATION,
      BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_REVIEW
    ]
  );
  const sortedButtons = [...taskFilteredButtons].sort((a, b) => {
    const priorityA = CreatebuttonPriority[a.MDG_MAT_DYN_BTN_ACTION_TYPE] ?? 999;
    const priorityB = CreatebuttonPriority[b.MDG_MAT_DYN_BTN_ACTION_TYPE] ?? 999;
    return priorityA - priorityB;
  });

const dedupedButtons = [];
const seen = new Set();
for (const btn of sortedButtons) {
  if (!seen.has(btn.MDG_MAT_DYN_BTN_ACTION_TYPE)) {
    dedupedButtons.push(btn);
    seen.add(btn.MDG_MAT_DYN_BTN_ACTION_TYPE);
  }
}

useEffect(() => {
  if (
    dedupedButtons.find((btn) => btn.MDG_MAT_DYN_BTN_BUTTON_NAME === BUTTON_NAME.SEND_BACK) ||
    dedupedButtons.find((btn) => btn.MDG_MAT_DYN_BTN_BUTTON_NAME === BUTTON_NAME.CORRECTION)
  ) {
    setShowWfLevels(true);
  }
}, [dedupedButtons]);

  return (
    <div>
      <div
        style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}
      >
        <Box
          sx={{
            position: isGridZoomed ? "fixed" : "relative",
            top: isGridZoomed ? 0 : "auto",
            left: isGridZoomed ? 0 : "auto",
            right: isGridZoomed ? 0 : "auto",
            bottom: isGridZoomed ? 0 : "auto",
            width: isGridZoomed ? "100vw" : "100%",
            height: isGridZoomed ? "100vh" : "auto",
            zIndex: isGridZoomed ? 1004 : undefined,
            backgroundColor: isGridZoomed ? "white" : "transparent",
            padding: isGridZoomed ? "20px" : "0",
            display: "flex",
            flexDirection: "column",
            boxShadow: isGridZoomed
              ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
              : "none",
            transition: "all 0.3s ease",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("List of BOM")}</Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Button
                variant="contained"
                color="primary"
                size="small"
                disabled={disableCheck || !isAddRowEnabled}
                onClick={() => {
                  if (
                    payloadFields?.RequestType === REQUEST_TYPE.CREATE
                  ) {
                    setOpenAddMatPopup(true);
                  }
                }}
              >
                + {t("Add")}
              </Button>
              <Tooltip
                title={isGridZoomed ? t("Exit Zoom") : t("Zoom In")}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleGridZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <DataGrid
                rows={rows}
                columns={columns}
                pageSize={50}
                autoHeight={false}
                page={page}
                rowCount={paginationData?.totalElements || 0}
                rowsPerPageOptions={[50]}
                onRowClick={handleRowSelection}
                onCellEditCommit={handleCellEdit}
                onPageChange={(newPage) => handlePageChange(newPage)}
                pagination
                disableSelectionOnClick
                getRowClassName={(params) =>
                  params.id === selectedRowID ? "selected-row" : ""
                }
                style={{
                  border: "1px solid #ccc",
                  borderRadius: "8px",
                  width: "100%",
                  height: isGridZoomed
                    ? "calc(100vh - 150px)"
                    : `${Math.min(rows.length * 50 + 130, 300)}px`,
                  overflow: "auto",
                }}
                sx={{
                  "& .selected-row": {
                    backgroundColor: "rgb(234 233 255)",
                  },
                }}
              />
            </div>
          </div>
        </Box>
      </div>

      {selectedRowID && rows.find((row) => row.id === selectedRowID) && (
        <BOMViews
          isTabsZoomed={isTabsZoomed}
          toggleTabsZoom={toggleTabsZoom}
          viewNames={viewNames}
          selectedRowID={selectedRowID}
          viewsDt={dtData?.result}
          t={t}
        />
      )}
      {openAddMatPopup && (
        <AddBOMDialog
          open={openAddMatPopup}
          onClose={() => setOpenAddMatPopup(false)}
          withReference={withReference}
          setWithReference={setWithReference}
          onProceed={AddBOM}
          t={t}
        />
      )}
      <BottomNavGlobal
        filteredButtons={dedupedButtons}
        moduleName={MODULE_MAP.BOM}
        handleSaveAsDraft={handleBottomButton}
        handleSubmitForReview={handleBottomButton}
        handleSubmitForApprove={handleBottomButton}
        handleSendBack={() => {}}
        handleCorrection={() => {}}
        handleRejectAndCancel={() => {}}
        handleValidateAndSyndicate={
          // const allValidated = rows.length > 0 && rows.every(row => validatedRows[row.id] === true);
          // if (allValidated) {
            handleBottomButton
            
          // } else {
          //   showSnackbar(t("Please validate all rows before SAP Validation."), "error");
          // }
        }
        validateAllRows={() => {}}
        showWfLevels = {showWfLevels}
        selectedLevel={selectedLevel}
        workFlowLevels={wfLevels}
        setSelectedLevel={setSelectedLevel}
      />
      <Dialog open={dupDialogOpen} onClose={() => setDupDialogOpen(false)}>
        <DialogTitle>Duplicate BOM Found</DialogTitle>
        <DialogContent>
          <div style={{ whiteSpace: 'pre-line' }}>{dupDialogMsg}</div>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDupDialogOpen(false)} color="primary" autoFocus>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default BOMListDetails;

import { getCurrentDate } from "@helper/helper";
import { useDispatch, useSelector } from "react-redux";
import {
    setCreateChangeLogDataBOM,
    setCreatePayloadCopyForChangeLog,
    clearCreateChangeLogDataBOM
} from "@app/changeLogReducer";
import { updateTabFieldValues, setTabRows } from "@modules/BillOfMaterial/bomSlice";

export const useChangeLogCreationBOM = () => {
    const dispatch = useDispatch();
    const createPayloadCopyForChangeLog = useSelector((state) => state.changeLog.createPayloadCopyForChangeLog || {});
    const changeLogBOM = useSelector((state) => state.changeLog.createChangeLogDataBOM || {});
    const userData = useSelector((state) => state.userManagement.userData);
    const bomRows = useSelector((state) => state.bom.bomRows || []);

    const updateChangeLogBOM = ({
        rowId,
        viewName,
        fieldName,
        jsonName,
        currentValue,
        itemId = null, // For array items like Material/Document tabs
        itemIndex = null // For array items position
    }) => {
        const CURRENT_DATE = getCurrentDate().sapFormat;

        // Get previous value based on BOM tabFieldValues structure
        let previousValue;
        if (itemId || itemIndex !== null) {
            // For array items (Material/Document tabs)
            previousValue = getPreviousValueBOMArray(
                rowId,
                viewName,
                fieldName,
                createPayloadCopyForChangeLog,
                itemId,
                itemIndex
            );
        } else {
            // For object items (General tab, ChildRequestHeader)
            previousValue = getPreviousValueBOMObject(
                rowId,
                viewName,
                fieldName,
                createPayloadCopyForChangeLog
            );
        }

        // Find the BOM row to get ObjectNo
        const bomRow = bomRows.find(row => row.id === rowId);
        const ObjectNo = bomRow?.BOMNo || bomRow?.BOMId || bomRow?.id || rowId;

        // Create unique identifier for the change log entry
        const uniqueIdentifier = itemId
            ? `${ObjectNo}_${viewName}_${fieldName}_${itemId}`
            : itemIndex !== null
            ? `${ObjectNo}_${viewName}_${fieldName}_${itemIndex}`
            : `${ObjectNo}_${viewName}_${fieldName}`;

        const newChangeEntry = {
            ObjectNo,
            ChangedBy: userData?.emailId,
            ChangedOn: CURRENT_DATE,
            FieldName: fieldName,
            PreviousValue: previousValue,
            CurrentValue: currentValue,
            SAPValue: previousValue,
            ViewName: viewName,
            JsonName: jsonName,
            ItemId: itemId,
            ItemIndex: itemIndex,
            UniqueIdentifier: uniqueIdentifier
        };

        const existingLogs = changeLogBOM?.[rowId]?.changeLog || [];
        const existingIndex = existingLogs.findIndex(
            (entry) => entry.UniqueIdentifier === uniqueIdentifier
        );

        let updatedLogs;

        if (existingIndex !== -1) {
            // Update existing entry
            updatedLogs = [...existingLogs];
            updatedLogs[existingIndex] = {
                ...updatedLogs[existingIndex],
                CurrentValue: currentValue,
                ChangedOn: CURRENT_DATE,
            };
        } else {
            // Add new entry
            updatedLogs = [...existingLogs, newChangeEntry];
        }

        dispatch(setCreateChangeLogDataBOM({
            uniqueId: rowId,
            changeLog: updatedLogs
        }));
    };

    // Function to initialize the payload copy for change log tracking
    const initializeBOMChangeLogPayload = (tabFieldValues) => {
        try {
            // Create a deep copy of tabFieldValues for change log tracking
            const payloadCopy = JSON.parse(JSON.stringify(tabFieldValues));

            dispatch(setCreatePayloadCopyForChangeLog(payloadCopy));
        } catch (error) {
            console.error("Error initializing BOM change log payload:", error);
        }
    };

    // Function to clear BOM change log data
    const clearBOMChangeLog = () => {
        dispatch(clearCreateChangeLogDataBOM());
    };

    // Enhanced wrapper for updateTabFieldValues that includes change log tracking
    const updateTabFieldValuesWithChangeLog = ({
        rowId,
        viewName,
        fieldName,
        value,
        jsonName = fieldName // Default to fieldName if jsonName not provided
    }) => {
        // First update the field value in Redux
        dispatch(updateTabFieldValues({
            rowId,
            viewName,
            fieldName,
            value
        }));

        // Then track the change in change log
        updateChangeLogBOM({
            rowId,
            viewName,
            fieldName,
            jsonName,
            currentValue: value
        });
    };

    // Enhanced wrapper for setTabRows that includes change log tracking for array updates
    const setTabRowsWithChangeLog = ({
        rowId,
        viewName,
        rows,
        changedRowIndex = null,
        changedFieldName = null,
        changedValue = null
    }) => {
        // First update the rows in Redux
        dispatch(setTabRows({
            rowId,
            viewName,
            rows
        }));

        // If specific field change is provided, track it in change log
        if (changedRowIndex !== null && changedFieldName && changedValue !== null) {
            const itemId = rows[changedRowIndex]?.id || rows[changedRowIndex]?.BOMItemId;

            updateChangeLogBOM({
                rowId,
                viewName,
                fieldName: changedFieldName,
                jsonName: changedFieldName,
                currentValue: changedValue,
                itemId,
                itemIndex: changedRowIndex
            });
        }
    };

    return {
        updateChangeLogBOM,
        initializeBOMChangeLogPayload,
        clearBOMChangeLog,
        updateTabFieldValuesWithChangeLog,
        setTabRowsWithChangeLog
    };
};

// Helper function for getting previous values from array data (Material/Document tabs)
const getPreviousValueBOMArray = (
    rowId,
    viewName,
    fieldName,
    createPayloadCopyForChangeLog,
    itemId,
    itemIndex
) => {
    try {
        const rowData = createPayloadCopyForChangeLog?.[rowId];
        if (!rowData) return "-";

        const viewData = rowData[viewName];
        if (!viewData || !Array.isArray(viewData)) return "-";

        // Find by itemId first (preferred method)
        if (itemId) {
            const item = viewData.find(item =>
                item.id === itemId ||
                item.BOMItemId === itemId ||
                item.MaterialId === itemId ||
                item.DocumentId === itemId
            );
            return item?.[fieldName] || "-";
        }

        // Fall back to index
        if (itemIndex !== null && viewData[itemIndex]) {
            return viewData[itemIndex][fieldName] || "-";
        }

        return "-";
    } catch (error) {
        return "-";
    }
};

// Helper function for getting previous values from object data (General tab, ChildRequestHeader)
const getPreviousValueBOMObject = (
    rowId,
    viewName,
    fieldName,
    createPayloadCopyForChangeLog
) => {
    try {
        const rowData = createPayloadCopyForChangeLog?.[rowId];
        if (!rowData) return "-";

        const viewData = rowData[viewName];
        if (!viewData) return "-";

        // For object data (like ChildRequestHeader)
        if (typeof viewData === 'object' && !Array.isArray(viewData)) {
            return viewData[fieldName] || "-";
        }

        // For General tab which might be an array of objects
        if (Array.isArray(viewData)) {
            // General tab structure might vary, this is a basic implementation
            // You might need to adjust based on how General tab data is structured
            const firstItem = viewData[0];
            return firstItem?.[fieldName] || "-";
        }

        return "-";
    } catch (error) {
        return "-";
    }
};

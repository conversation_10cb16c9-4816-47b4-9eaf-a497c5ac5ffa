# BOM Change Log Implementation Guide

This document explains how to use the BOM change log functionality that tracks changes to `tabFieldValues` in the Bill of Materials module.

## Overview

The BOM change log system tracks changes to the `tabFieldValues` structure, which includes:
- **Material tab**: Array of material items
- **Document tab**: Array of document items  
- **General tab**: Array of general information
- **ChildRequestHeader**: Object with request header data

## Files Created/Modified

### 1. Change Log Reducer (`app/src/app/changeLogReducer.jsx`)
- Added `createChangeLogDataBOM` to initial state
- Added `setCreateChangeLogDataBOM` reducer
- Added `clearCreateChangeLogDataBOM` reducer

### 2. Helper Functions (`app/src/helper/helper.js`)
- Added `getPreviousValueBOM` function for object data
- Enhanced with BOM-specific previous value retrieval

### 3. BOM Change Log Hook (`app/src/hooks/useChangeLogCreationBOM.js`)
- Main hook for BOM change log functionality
- Provides functions for tracking field changes
- Includes enhanced wrappers for existing Redux actions

### 4. Example Component (`app/src/modules/BillOfMaterial/components/BOMViewsTableWithChangeLog.jsx`)
- Example implementation showing how to integrate change log tracking
- Enhanced version of BOMViewsTable with change log support

## Usage

### 1. Basic Setup

```javascript
import { useChangeLogCreationBOM } from "@hooks/useChangeLogCreationBOM";

const MyBOMComponent = () => {
  const { 
    updateChangeLogBOM, 
    initializeBOMChangeLogPayload,
    clearBOMChangeLog,
    updateTabFieldValuesWithChangeLog,
    setTabRowsWithChangeLog
  } = useChangeLogCreationBOM();

  // Initialize change log tracking when component mounts
  useEffect(() => {
    const tabFieldValues = useSelector((state) => state.bom.tabFieldValues);
    initializeBOMChangeLogPayload(tabFieldValues);
  }, []);
};
```

### 2. Tracking Individual Field Changes

For simple field updates (like ChildRequestHeader fields):

```javascript
// Instead of using updateTabFieldValues directly:
dispatch(updateTabFieldValues({
  rowId: "row123",
  viewName: "ChildRequestHeader", 
  fieldName: "TaskId",
  value: "TASK001"
}));

// Use the enhanced version with change log:
updateTabFieldValuesWithChangeLog({
  rowId: "row123",
  viewName: "ChildRequestHeader",
  fieldName: "TaskId", 
  value: "TASK001",
  jsonName: "TaskId" // Optional, defaults to fieldName
});
```

### 3. Tracking Array Changes (Material/Document Tabs)

For array updates with specific field changes:

```javascript
// When updating a specific field in an array item:
const updatedRows = rows.map((row, index) =>
  row.id === targetRowId 
    ? { ...row, Component: newValue }
    : row
);

// Use enhanced setTabRows with change log:
setTabRowsWithChangeLog({
  rowId: selectedRowID,
  viewName: "Material", // or "Document"
  rows: updatedRows,
  changedRowIndex: targetIndex,
  changedFieldName: "Component",
  changedValue: newValue
});
```

### 4. Manual Change Log Updates

For complex scenarios, use the core function directly:

```javascript
updateChangeLogBOM({
  rowId: "row123",
  viewName: "Material",
  fieldName: "Component",
  jsonName: "Component",
  currentValue: "MAT001",
  itemId: "material-item-456", // For array items
  itemIndex: 2 // Alternative to itemId
});
```

## Data Structure

### Change Log Entry Structure

Each change log entry contains:

```javascript
{
  ObjectNo: "BOM123", // From bomRow.BOMNo or bomRow.id
  ChangedBy: "<EMAIL>",
  ChangedOn: "20241128", // SAP format date
  FieldName: "Component",
  PreviousValue: "MAT001",
  CurrentValue: "MAT002", 
  SAPValue: "MAT001", // Same as PreviousValue
  ViewName: "Material",
  JsonName: "Component",
  ItemId: "material-item-456", // For array items
  ItemIndex: 2, // For array items
  UniqueIdentifier: "BOM123_Material_Component_material-item-456"
}
```

### Redux State Structure

```javascript
state.changeLog.createChangeLogDataBOM = {
  "row123": {
    changeLog: [
      // Array of change log entries for this row
    ]
  },
  "row456": {
    changeLog: [
      // Array of change log entries for this row  
    ]
  }
}
```

## Previous Value Retrieval

The system retrieves previous values from `createPayloadCopyForChangeLog` based on the BOM structure:

### For Object Data (ChildRequestHeader, etc.)
```javascript
createPayloadCopyForChangeLog[rowId][viewName][fieldName]
```

### For Array Data (Material/Document tabs)
```javascript
// By itemId (preferred)
createPayloadCopyForChangeLog[rowId][viewName].find(item => item.id === itemId)[fieldName]

// By index (fallback)
createPayloadCopyForChangeLog[rowId][viewName][itemIndex][fieldName]
```

## Integration with Existing Components

To add change log tracking to existing BOM components:

1. Import the hook: `import { useChangeLogCreationBOM } from "@hooks/useChangeLogCreationBOM";`
2. Initialize change log tracking in useEffect
3. Replace direct Redux dispatches with enhanced wrapper functions
4. For custom field update logic, call `updateChangeLogBOM` manually

## Best Practices

1. **Initialize Early**: Call `initializeBOMChangeLogPayload` when the component mounts or when tabFieldValues are first loaded
2. **Use Wrappers**: Prefer `updateTabFieldValuesWithChangeLog` and `setTabRowsWithChangeLog` over manual calls
3. **Unique Identifiers**: Ensure array items have stable `id` or `BOMItemId` fields for proper tracking
4. **Clear on Exit**: Call `clearBOMChangeLog()` when leaving the BOM module or starting a new request

## Error Handling

The change log functions include try-catch blocks and return "-" for missing previous values. Monitor console for any initialization errors.

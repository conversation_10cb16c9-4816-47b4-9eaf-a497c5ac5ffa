import React, { useEffect, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Checkbox,
  Button,
  Box,
  Typography,
  IconButton,
} from "@mui/material";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import SingleSelectDropdown from "@components/SingleSelectDropdown/SingleSelectDropdown";
import { colors } from "@theme/colors";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import { useChangeLogCreationBOM } from "@hooks/useChangeLogCreationBOM";

// This is an enhanced version of BOMViewsTable that includes change log tracking
const BOMViewsTableWithChangeLog = ({ viewsDt, activeViewName, rows, onRowsUpdate }) => {
  const fields = viewsDt
    ?.flatMap((item) => item.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE)
    ?.filter((field) => field.MDG_VIEW_NAME === activeViewName)
    ?.sort(
      (a, b) => (a.MDG_SEQUENCE_NO ?? 0) - (b.MDG_SEQUENCE_NO ?? 0)
    );
  const allDropDownData = useSelector((state) => state.bom.dropDownData || {});
  const cardName = viewsDt
    ?.flatMap((item) => item.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE)
    ?.find(
      (field) => field.MDG_VIEW_NAME === activeViewName
    )?.MDG_CARD_NAME;
  const selectedRowID = useSelector((state) => state.bom.selectedRowID);
  const parentBomRow = useSelector((state) => 
    state.bom.bomRows.find(row => row.id === selectedRowID)
  );
  const dispatch = useDispatch();
  const lastPlantRef = useRef({});
  const [componentOptions, setComponentOptions] = useState({});
  const [disableCheck, setDisableCheck] = useState(false);

  // Initialize the change log hook
  const { 
    updateChangeLogBOM, 
    initializeBOMChangeLogPayload, 
    setTabRowsWithChangeLog 
  } = useChangeLogCreationBOM();

  // Initialize change log tracking when component mounts
  useEffect(() => {
    const tabFieldValues = useSelector((state) => state.bom.tabFieldValues);
    initializeBOMChangeLogPayload(tabFieldValues);
  }, []);

  // Enhanced version of onRowsUpdate that includes change log tracking
  const handleRowsUpdateWithChangeLog = (updatedRows, changedRowIndex, changedFieldName, changedValue) => {
    // Use the enhanced setTabRowsWithChangeLog function
    setTabRowsWithChangeLog({
      rowId: selectedRowID,
      viewName: activeViewName,
      rows: updatedRows,
      changedRowIndex,
      changedFieldName,
      changedValue
    });
  };

  // Render a cell with the appropriate input component based on field type
  const renderCell = (field, rowId, value, isDisabled = false) => {
    const fieldName = field.MDG_JSON_FIELD_NAME || field.MDG_UI_FIELD_NAME;

    // Special handling for Component field
    if (fieldName === "Component") {
      const plant = parentBomRow?.plant || parentBomRow?.Plant;
      const compositeKey = `${selectedRowID}_${rowId}`;
      const optionsWithFallback = componentOptions[compositeKey] || [];
      const componentValue = value || "";

      return (
        <SingleSelectDropdown
          options={optionsWithFallback}
          value={componentValue}
          onChange={(newValue) => {
            const codeValue = newValue?.code || "";
            const updatedRows = rows.map((row, index) =>
              row.id === rowId 
                ? { ...row, [fieldName]: codeValue } 
                : row
            );
            
            // Find the index of the changed row
            const changedRowIndex = rows.findIndex(row => row.id === rowId);
            
            // Call the enhanced update function with change log tracking
            handleRowsUpdateWithChangeLog(
              updatedRows, 
              changedRowIndex, 
              fieldName, 
              codeValue
            );
          }}
          placeholder={`SELECT ${field.MDG_UI_FIELD_NAME.toUpperCase()}`}
          disabled={isDisabled || disableCheck}
        />
      );
    }

    switch (field.MDG_FIELD_TYPE) {
      case "Input":
        return (
          <TextField
            size="small"
            value={value || ""}
            onChange={(e) => {
              const updatedRows = rows.map((row) =>
                row.id === rowId ? { ...row, [fieldName]: e.target.value } : row
              );
              
              // Find the index of the changed row
              const changedRowIndex = rows.findIndex(row => row.id === rowId);
              
              // Call the enhanced update function with change log tracking
              handleRowsUpdateWithChangeLog(
                updatedRows, 
                changedRowIndex, 
                fieldName, 
                e.target.value
              );
            }}
            fullWidth
            variant="outlined"
            placeholder={`ENTER ${field.MDG_UI_FIELD_NAME.toUpperCase()}`}
            disabled={isDisabled || disableCheck}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  borderColor: colors.primary.pale,
                },
                "&:hover fieldset": {
                  borderColor: colors.primary.border,
                },
                "&.Mui-focused fieldset": {
                  borderColor: colors.primary.main,
                },
              },
            }}
          />
        );
      case "Calendar":
        return (
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              value={value ? dayjs(value) : null}
              onChange={(newValue) => {
                const updatedRows = rows.map((row) =>
                  row.id === rowId 
                    ? { ...row, [fieldName]: newValue }
                    : row
                );
                
                // Find the index of the changed row
                const changedRowIndex = rows.findIndex(row => row.id === rowId);
                
                // Call the enhanced update function with change log tracking
                handleRowsUpdateWithChangeLog(
                  updatedRows, 
                  changedRowIndex, 
                  fieldName, 
                  newValue
                );
              }}
              disabled={isDisabled || disableCheck}
              format="DD/MM/YYYY"
              slotProps={{
                textField: {
                  size: "small",
                  fullWidth: true,
                  placeholder: `SELECT ${field.MDG_UI_FIELD_NAME.toUpperCase()}`,
                  sx: {
                    "& .MuiOutlinedInput-root": {
                      "& fieldset": {
                        borderColor: colors.primary.pale,
                      },
                      "&:hover fieldset": {
                        borderColor: colors.primary.border,
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: colors.primary.main,
                      },
                    },
                  },
                },
              }}
            />
          </LocalizationProvider>
        );
      // Add other field types as needed
      default:
        return <span>{value || ""}</span>;
    }
  };

  return (
    <Box sx={{ width: "100%" }}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {cardName || activeViewName}
      </Typography>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              {fields?.map((field) => (
                <TableCell key={field.MDG_UI_FIELD_NAME}>
                  {field.MDG_UI_FIELD_NAME}
                  {field.MDG_VISIBILITY === "Mandatory" && (
                    <span style={{ color: "red" }}>*</span>
                  )}
                </TableCell>
              ))}
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.map((row) => (
              <TableRow key={row.id}>
                {fields?.map((field) => {
                  const fieldName = field.MDG_JSON_FIELD_NAME || field.MDG_UI_FIELD_NAME;
                  return (
                    <TableCell key={`${row.id}-${fieldName}`}>
                      {renderCell(field, row.id, row[fieldName])}
                    </TableCell>
                  );
                })}
                <TableCell>
                  <IconButton
                    onClick={() => {
                      const updatedRows = rows.filter((r) => r.id !== row.id);
                      onRowsUpdate(updatedRows);
                    }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Button
        startIcon={<AddIcon />}
        onClick={() => {
          const newRow = { id: `${activeViewName.toLowerCase()}-${Date.now()}` };
          fields?.forEach((field) => {
            const fieldName = field.MDG_JSON_FIELD_NAME || field.MDG_UI_FIELD_NAME;
            newRow[fieldName] = "";
          });
          onRowsUpdate([...rows, newRow]);
        }}
        sx={{ mt: 2 }}
      >
        Add Row
      </Button>
    </Box>
  );
};

export default BOMViewsTableWithChangeLog;

import React, { useState, useEffect, useRef } from "react";
import { DataGrid } from "@mui/x-data-grid";
import {
  Typography,
  Button,
  Box,
  IconButton,
  Paper,
  Chip,
  Stack,
  Tooltip,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import TextField from "@mui/material/TextField";
import Checkbox from "@mui/material/Checkbox";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { styled } from "@mui/material/styles";
import { colors } from "@constant/colors";
import TableRowsIcon from "@mui/icons-material/TableRows";
import { useSelector, useDispatch } from "react-redux";
import { ERROR_MESSAGES } from "@constant/enum";
import { doAjax } from "@components/Common/fetchService";
import { destination_BOM } from "../../../destinationVariables";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { ENABLE_STATUSES } from "../../../constant/enum";
import { useLocation } from "react-router-dom";

const StyledDataGridContainer = styled(Paper)(({ theme }) => ({
  boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
  borderRadius: "16px",
  overflow: "hidden",
  border: `1px solid ${colors.primary.border}`,
  "& .MuiDataGrid-root": {
    border: "none",
    "& .MuiDataGrid-cell": {
      borderBottom: `1px solid ${colors.primary.pale}`,
      padding: "8px 16px",
      "&:focus": {
        outline: "none",
      },
      "&:focus-within": {
        outline: `2px solid ${colors.primary.main}`,
        outlineOffset: "-2px",
      },
    },
    "& .MuiDataGrid-columnHeaders": {
      backgroundColor: colors.primary.ultraLight,
      borderBottom: `2px solid ${colors.primary.border}`,
      "& .MuiDataGrid-columnHeader": {
        padding: "12px 16px",
        "&:focus": {
          outline: "none",
        },
        "&:focus-within": {
          outline: `2px solid ${colors.primary.main}`,
          outlineOffset: "-2px",
        },
      },
    },
    "& .MuiDataGrid-row": {
      "&:nth-of-type(even)": {
        backgroundColor: colors.primary.veryLight,
      },
      "&:hover": {
        backgroundColor: `${colors.primary.light}40`,
      },
      "&.Mui-selected": {
        backgroundColor: `${colors.primary.main}20`,
        "&:hover": {
          backgroundColor: `${colors.primary.main}30`,
        },
      },
    },
    "& .MuiDataGrid-virtualScroller": {
      "&::-webkit-scrollbar": {
        width: "8px",
        height: "8px",
      },
      "&::-webkit-scrollbar-track": {
        backgroundColor: colors.primary.veryLight,
      },
      "&::-webkit-scrollbar-thumb": {
        backgroundColor: colors.primary.pale,
        borderRadius: "4px",
        "&:hover": {
          backgroundColor: colors.primary.border,
        },
      },
    },
  },
}));

const StyledToolbar = styled(Box)(({ theme }) => ({
  padding: "16px 20px",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  borderBottom: `1px solid ${colors.primary.border}`,
  backgroundColor: colors.primary.ultraLight,
  gap: "16px",
}));

const StyledNoRowsOverlay = styled("div")(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  height: "100%",
  padding: "32px",
  backgroundColor: colors.primary.veryLight,
  "& svg": {
    color: colors.secondary.grey,
    fontSize: "48px",
    marginBottom: "16px",
  },
}));

function CustomNoRowsOverlay() {
  return (
    <StyledNoRowsOverlay>
      <TableRowsIcon />
      <Typography
        variant="h6"
        sx={{
          color: colors.secondary.grey,
          fontWeight: 500,
          marginBottom: "8px",
        }}
      >
        {ERROR_MESSAGES.NO_DATA_AVAILABLE}
      </Typography>
      <Typography
        variant="body2"
        sx={{
          color: colors.secondary.grey,
          textAlign: "center",
        }}
      >
        {ERROR_MESSAGES.NO_RECORDS}
      </Typography>
    </StyledNoRowsOverlay>
  );
}

const BOMViewsTable = ({ viewsDt, activeViewName, rows, onRowsUpdate }) => {
  const fields = viewsDt
    ?.flatMap((item) => item.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE)
    ?.filter((field) => field.MDG_VIEW_NAME === activeViewName)
    ?.sort(
      (a, b) => (a.MDG_SEQUENCE_NO ?? 0) - (b.MDG_SEQUENCE_NO ?? 0)
    );
  const allDropDownData = useSelector((state) => state.bom.dropDownData || {});
  const cardName = viewsDt
    ?.flatMap((item) => item.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE)
    ?.find(
      (field) => field.MDG_VIEW_NAME === activeViewName
    )?.MDG_CARD_NAME;

  const selectedRowID = useSelector((state) => state.bom.selectedRowID);
  const bomRows = useSelector((state) => state.bom.bomRows);
  const parentBomRow = bomRows.find(row => row.id === selectedRowID);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isrequestId = queryParams.get("RequestId");
  const requestStatus = useSelector((state) => state.bom.BOMpayloadData?.RequestStatus);
  console.log(requestStatus, "requestStatus");
  const [componentOptions, setComponentOptions] = useState({});
  const lastPlantRef = useRef({});
  const disableCheck =
      !ENABLE_STATUSES.includes(requestStatus) ||
      (isrequestId && !isreqBench);

  const fetchComponentOptions = (plant, compositeKey) => {
    if (!plant) {
      setComponentOptions((prev) => ({ ...prev, [compositeKey]: [] }));
      return;
    }
    doAjax(
      `/${destination_BOM}/data/getBOMComponentBasedOnPlant?plant=${encodeURIComponent(plant)}`,
      "get",
      (data) => {
        const mappedOptions = (data.body || []).map(opt => ({
          code: opt.code,
          desc: opt.desc || opt.code,
          ...opt
        }));
        setComponentOptions((prev) => ({ ...prev, [compositeKey]: mappedOptions }));
      },
      (error) => {
        setComponentOptions((prev) => ({ ...prev, [compositeKey]: [] }));
        console.error(error);
      }
    );
  };

  useEffect(() => {
    rows.forEach((row) => {
      const plant = parentBomRow?.plant || parentBomRow?.Plant;
      const compositeKey = `${selectedRowID}_${row.id}`;
      if (lastPlantRef.current[compositeKey] !== plant) {
        lastPlantRef.current[compositeKey] = plant;
        fetchComponentOptions(plant, compositeKey);
      }
    });
  }, [rows, parentBomRow, selectedRowID]);

  useEffect(() => {
    let shouldUpdate = false;
    const updatedRows = rows.map((row) => {
      const plant = parentBomRow?.plant || parentBomRow?.Plant;
      if ((!plant || plant === "") && row.Component && row.Component !== "") {
        shouldUpdate = true;
        return { ...row, Component: "" };
      }
      return row;
    });
    if (shouldUpdate) {
      onRowsUpdate(updatedRows);
    }
  }, [rows, parentBomRow]);

  if (!fields || fields.length === 0) {
    return (
      <StyledDataGridContainer>
        <StyledNoRowsOverlay>
          <TableRowsIcon />
          <Typography
            variant="h6"
            sx={{
              color: colors.secondary.grey,
              fontWeight: 500,
              marginBottom: "8px",
            }}
          >
            {ERROR_MESSAGES.NO_FIELDS_CONFIGURED}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: colors.secondary.grey,
              textAlign: "center",
            }}
          >
            {ERROR_MESSAGES.NO_DATA_CONFIGURED}
          </Typography>
        </StyledNoRowsOverlay>
      </StyledDataGridContainer>
    );
  }

  const handleAddRow = () => {
    const newRow = {
      id: Date.now(),
      ...fields.reduce((acc, field) => {
        acc[field.MDG_JSON_FIELD_NAME || field.MDG_UI_FIELD_NAME] = "";
        return acc;
      }, {}),
    };
    onRowsUpdate([...rows, newRow]);
  };

  const handleDeleteRow = (rowId) => {
    const updatedRows = rows.filter((row) => row.id !== rowId);
    onRowsUpdate(updatedRows);
  };

  const renderCell = (field, value, rowId) => {
    const fieldName =
      field.MDG_JSON_FIELD_NAME || field.MDG_UI_FIELD_NAME;
    const isDisabled = field.MDG_VISIBILITY === "Display";

    if (
      field.MDG_FIELD_TYPE === "Drop Down" &&
      (fieldName === "Component" || fieldName === "component")
    ) {
      const compositeKey = `${selectedRowID}_${rowId}`;
      const componentValue =
        (componentOptions[compositeKey] || []).find(opt => opt.code === value) ||
        (value
          ? { code: value, desc: "" }
          : "");

      const optionsWithFallback =
        componentValue && componentValue.code && !(componentOptions[compositeKey] || []).some(opt => opt.code === componentValue.code)
          ? [...(componentOptions[compositeKey] || []), componentValue]
          : (componentOptions[compositeKey] || []);

      return (
        <SingleSelectDropdown
          options={optionsWithFallback}
          value={componentValue}
          onChange={(newValue) => {
            const codeValue = newValue?.code || "";
            const updatedRows = rows.map((row) =>
              row.id === rowId ? { ...row, [fieldName]: codeValue } : row
            );
            onRowsUpdate(updatedRows);
          }}
          placeholder={`SELECT ${field.MDG_UI_FIELD_NAME.toUpperCase()}`}
          disabled={isDisabled || disableCheck}
        />
      );
    }

    switch (field.MDG_FIELD_TYPE) {
      case "Input":
        return (
          <TextField
            size="small"
            value={value || ""}
            onChange={(e) => {
              const updatedRows = rows.map((row) =>
                row.id === rowId ? { ...row, [fieldName]: e.target.value } : row
              );
              onRowsUpdate(updatedRows);
            }}
            fullWidth
            variant="outlined"
            placeholder={`ENTER ${field.MDG_UI_FIELD_NAME.toUpperCase()}`}
            disabled={isDisabled || disableCheck}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  borderColor: colors.primary.pale,
                },
                "&:hover fieldset": {
                  borderColor: colors.primary.border,
                },
                "&.Mui-focused fieldset": {
                  borderColor: colors.primary.main,
                },
              },
            }}
          />
        );
      case "Calendar":
        return (
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              value={value ? dayjs(value) : null}
              onChange={(newValue) => {
                const updatedRows = rows.map((row) =>
                  row.id === rowId 
                    ? { ...row, [fieldName]: newValue }
                    : row
                );
                onRowsUpdate(updatedRows);
              }}
              disabled={isDisabled || disableCheck}
              format="DD/MM/YYYY"
              slotProps={{
                textField: {
                  size: "small",
                  fullWidth: true,
                  placeholder: `SELECT ${field.MDG_UI_FIELD_NAME.toUpperCase()}`,
                  sx: {
                    "& .MuiOutlinedInput-root": {
                      "& fieldset": {
                        borderColor: colors.primary.pale,
                      },
                      "&:hover fieldset": {
                        borderColor: colors.primary.border,
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: colors.primary.main,
                      },
                    },
                  },
                },
              }}
            />
          </LocalizationProvider>
        );
      case "Drop Down":
        return (
          <SingleSelectDropdown
            options={allDropDownData[fieldName] || []}
            value={value || ""}
            onChange={(newValue) => {
              const codeValue = newValue?.code || "";
              const updatedRows = rows.map((row) =>
                row.id === rowId ? { ...row, [fieldName]: codeValue } : row
              );
              onRowsUpdate(updatedRows);
            }}
            placeholder={`SELECT ${field.MDG_UI_FIELD_NAME.toUpperCase()}`}
            disabled={isDisabled || disableCheck}
          />
        );
      case "Check Box":
        return (
          <Checkbox
            checked={value || false}
            onChange={(e) => {
              const updatedRows = rows.map((row) =>
                row.id === rowId
                  ? { ...row, [fieldName]: e.target.checked }
                  : row
              );
              onRowsUpdate(updatedRows);
            }}
            sx={{
              color: colors.primary.main,
              "&.Mui-checked": {
                color: colors.primary.main,
              },
            }}
            disabled={isDisabled || disableCheck}
          />
        );
      default:
        return (
          <Typography
            variant="body2"
            sx={{
              color: value ? colors.text.primary : colors.text.disabled,
            }}
          >
            {value || "-"}
          </Typography>
        );
    }
  };

  const columns = [
    ...fields.map((field) => ({
      field: field.MDG_JSON_FIELD_NAME || field.MDG_UI_FIELD_NAME,
      headerName:
        field.MDG_VISIBILITY === "Mandatory" ? (
          <span>
            {field.MDG_UI_FIELD_NAME}
            <span style={{ color: "red", marginLeft: 2 }}>*</span>
          </span>
        ) : (
          field.MDG_UI_FIELD_NAME
        ),
      flex: 1,
      minWidth: 150,
      renderCell: (params) => renderCell(field, params.value, params.row.id),
    })),

    ...(activeViewName !== "General"
      ? [
          {
            field: "actions",
            headerName: "Actions",
            width: 100,
            sortable: false,
            filterable: false,
            renderCell: (params) => (
              <Tooltip title="Delete Row">
                <IconButton
                  onClick={() => handleDeleteRow(params.row.id)}
                  color="error"
                  size="small"
                  disabled={disableCheck}
                  sx={{
                    "&:hover": {
                      backgroundColor: `${colors.error.light}40`,
                    },
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            ),
          },
        ]
      : []),
  ];

  return (
    <StyledDataGridContainer>
      <StyledToolbar>
        <Stack direction="row" spacing={2} alignItems="center">
          {cardName && (
            <Typography
              variant="h6"
              sx={{
                color: colors.primary.main,
              }}
            >
              {cardName}
            </Typography>
          )}
          <Chip
            label={`${rows.length} records`}
            size="small"
            sx={{
              backgroundColor: colors.primary.light,
              color: colors.primary.main,
              height: "24px",
              fontSize: "0.75rem",
            }}
          />
        </Stack>

        {activeViewName !== "General" && (
          <Tooltip title="Add New Row">
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddRow}
              size="small"
              sx={{
                backgroundColor: colors.primary.main,
                "&:hover": {
                  backgroundColor: colors.primary.dark,
                },
                borderRadius: "8px",
                textTransform: "none",
                fontWeight: 500,
              }}
              disabled={disableCheck}
            >
              Add Row
            </Button>
          </Tooltip>
        )}
      </StyledToolbar>
      <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
        <div style={{ height: "100%" }}>
          <DataGrid
            columns={columns}
            rows={rows}
            autoHeight={false}
            disableSelectionOnClick
            hideFooter
            components={{
              NoRowsOverlay: CustomNoRowsOverlay,
            }}
            style={{
              border: "1px solid #ccc",
              borderRadius: "8px",
              width: "100%",
              height:
                rows.length === 0
                  ? "180px"
                  : `${Math.min(rows.length * 50 + 100, 300)}px`,
              overflow: "auto",
            }}
          />
        </div>
      </div>
    </StyledDataGridContainer>
  );
};

export default BOMViewsTable;
